# Simple script to create invoice data
$baseUrl = "http://localhost:8091/api"

Write-Host "Creating invoice data..." -ForegroundColor Green

# Create invoice directly with minimal data
$invoiceData = @{
    invoiceNumber = "RB/25-26/001"
    candidateName = "<PERSON><PERSON><PERSON><PERSON>dam"
    clientName = "Saurabh"
    billingAmount = 50000.00
    gstAmount = 9000.00
    invoiceAmount = 59000.00
    bdmCommission = "5%"
    invoiceDate = "2025-07-21"
    status = "DRAFT"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/invoices" -Method POST -Body $invoiceData -ContentType "application/json"
    Write-Host "Invoice created successfully!" -ForegroundColor Green
    Write-Host "Invoice ID: $($response.id)"
    Write-Host "Invoice Number: $($response.invoiceNumber)"
    Write-Host "Client: $($response.clientName)"
    Write-Host "Amount: $($response.invoiceAmount)"
} catch {
    Write-Host "Error creating invoice: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Yellow
    }
}
