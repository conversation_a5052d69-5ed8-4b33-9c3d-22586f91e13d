# Simple script to create a test invoice
$baseUrl = "http://localhost:8091/api"

Write-Host "Creating test invoice..." -ForegroundColor Green

# Create client
$clientData = @{
    name = "Saurabh"
    email = "<EMAIL>"
    phone = "+91-9876543210"
} | ConvertTo-Json

try {
    $client = Invoke-RestMethod -Uri "$baseUrl/clients" -Method POST -Body $clientData -ContentType "application/json"
    Write-Host "Client created with ID: $($client.id)"
    $clientId = $client.id
} catch {
    Write-Host "Error creating client: $($_.Exception.Message)"
    exit 1
}

# Create project
$projectData = @{
    name = "Software Development"
    clientId = $clientId
    description = "Development project"
} | ConvertTo-Json

try {
    $project = Invoke-RestMethod -Uri "$baseUrl/projects" -Method POST -Body $projectData -ContentType "application/json"
    Write-Host "Project created with ID: $($project.id)"
    $projectId = $project.id
} catch {
    Write-Host "Error creating project: $($_.Exception.Message)"
    exit 1
}

# Create candidate
$candidateData = @{
    name = "Prathamesh Kadam"
    email = "<EMAIL>"
    phone = "+91-9876543211"
} | ConvertTo-Json

try {
    $candidate = Invoke-RestMethod -Uri "$baseUrl/candidates" -Method POST -Body $candidateData -ContentType "application/json"
    Write-Host "Candidate created with ID: $($candidate.id)"
    $candidateId = $candidate.id
} catch {
    Write-Host "Error creating candidate: $($_.Exception.Message)"
    exit 1
}

# Get invoice types
try {
    $invoiceTypes = Invoke-RestMethod -Uri "$baseUrl/invoice-types" -Method GET
    $invoiceTypeId = $invoiceTypes[0].id
    Write-Host "Using invoice type ID: $invoiceTypeId"
} catch {
    Write-Host "Error getting invoice types: $($_.Exception.Message)"
    exit 1
}

# Create invoice
$invoiceData = @{
    invoiceNumber = "RB/25-26/001"
    clientId = $clientId
    projectId = $projectId
    candidateId = $candidateId
    invoiceTypeId = $invoiceTypeId
    billingAmount = 50000.00
    taxAmount = 9000.00
    totalAmount = 59000.00
    invoiceDate = "2025-07-21"
    dueDate = "2025-08-20"
    description = "Software development services"
} | ConvertTo-Json

try {
    $invoice = Invoke-RestMethod -Uri "$baseUrl/invoices" -Method POST -Body $invoiceData -ContentType "application/json"
    Write-Host "Invoice created successfully!"
    Write-Host "Invoice ID: $($invoice.id)"
    Write-Host "Invoice Number: $($invoice.invoiceNumber)"
} catch {
    Write-Host "Error creating invoice: $($_.Exception.Message)"
    exit 1
}

Write-Host "Test data setup completed!" -ForegroundColor Green
