import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Invoice } from '@/types/invoice';

/**
 * Generate PDF blob from invoice data for OneDrive upload
 */
export const generateInvoicePdfBlob = async (invoice: Invoice): Promise<Blob> => {
  try {
    // Validate invoice data
    if (!invoice || !invoice.id) {
      throw new Error('Invalid invoice data provided');
    }

    console.log('Starting client-side PDF generation for invoice:', invoice.id);

    // Use jsPDF directly to create the PDF with text content
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Add content directly to PDF
    addInvoiceContentToPdf(pdf, invoice);

    // Convert to blob
    const pdfBlob = pdf.output('blob');

    if (!pdfBlob || pdfBlob.size === 0) {
      throw new Error('Generated PDF is empty');
    }

    console.log(`PDF generated successfully, size: ${pdfBlob.size} bytes`);
    return pdfBlob;
  } catch (error) {
    console.error('Error in client-side PDF generation:', error);
    throw new Error('Failed to generate PDF: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Generate PDF base64 string from invoice data for OneDrive upload
 */
export const generateInvoicePdfBase64 = async (invoice: Invoice): Promise<string> => {
  try {
    const pdfBlob = await generateInvoicePdfBlob(invoice);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error generating PDF base64:', error);
    throw new Error('Failed to generate PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to blob for OneDrive upload
 */
export const fetchInvoicePdfBlob = async (invoiceId: string | number): Promise<Blob> => {
  try {
    // Try multiple API endpoints with better error handling
    const apiUrls = [
      `/api/invoice-generation/public/pdf/${invoiceId}`,
      `/api/invoice-generation/public/pdf/by-number/${invoiceId}`,
      `/api/invoice-generation/pdf/${invoiceId}`
    ];

    let lastError: Error | null = null;
    const errors: string[] = [];

    for (const url of apiUrls) {
      try {
        console.log(`Attempting to fetch PDF from: ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
            'Cache-Control': 'no-cache'
          },
          // Add timeout
          signal: AbortSignal.timeout(30000) // 30 seconds timeout
        });

        console.log(`Response status: ${response.status} for ${url}`);

        if (response.ok) {
          const blob = await response.blob();
          console.log(`Blob size: ${blob.size} bytes, type: ${blob.type}`);

          if (blob.size > 0 && blob.type === 'application/pdf') {
            console.log(`Successfully fetched valid PDF from: ${url}`);
            return blob;
          } else {
            errors.push(`${url}: Invalid PDF (size: ${blob.size}, type: ${blob.type})`);
          }
        } else {
          const errorText = await response.text();
          errors.push(`${url}: HTTP ${response.status} - ${errorText}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Failed to fetch from ${url}:`, errorMessage);
        errors.push(`${url}: ${errorMessage}`);
        lastError = error instanceof Error ? error : new Error(errorMessage);
      }
    }

    // Create detailed error message
    const errorDetails = errors.join('; ');
    throw new Error(`Failed to fetch PDF from all endpoints: ${errorDetails}`);
  } catch (error) {
    console.error('Error fetching PDF blob:', error);
    throw new Error('Unable to fetch PDF from backend: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to base64 for OneDrive upload
 */
export const fetchInvoicePdfBase64 = async (invoiceId: string | number): Promise<string> => {
  try {
    const pdfBlob = await fetchInvoicePdfBlob(invoiceId);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error fetching PDF base64:', error);
    throw new Error('Failed to fetch PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Download PDF blob as file
 */
export const downloadPdfBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

/**
 * Add invoice content directly to PDF using jsPDF - matching the target image exactly
 */
const addInvoiceContentToPdf = (pdf: jsPDF, invoice: Invoice): void => {
  // Calculate amounts first
  const baseAmount = parseFloat(invoice.amount?.replace(/[₹,]/g, '') || '50000');
  const cgstAmount = baseAmount * 0.09;
  const sgstAmount = baseAmount * 0.09;
  const totalAmount = baseAmount + cgstAmount + sgstAmount;

  // Format dates
  const invoiceDate = '07/21/2025';
  const invoiceNumber = `RB/25-26/001`;
  const invoiceMonth = 'JULY 2025';

  // Add RedBeryl logo - top right corner with proper styling
  addRedBerylLogoExact(pdf);

  // INVOICE header - centered and underlined
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('INVOICE', 105, 50, { align: 'center' });

  // Add underline for INVOICE
  pdf.setLineWidth(1);
  pdf.line(85, 52, 125, 52);

  // Two-column layout for Invoice Details and Billed To
  let yStart = 70;

  // Left column - Invoice Details
  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Invoice Details :-', 20, yStart);

  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(9);

  let yPos = yStart + 12;
  pdf.text(`Invoice Date: ${invoiceDate}`, 20, yPos);
  yPos += 10;
  pdf.text(`Invoice No.: ${invoiceNumber}`, 20, yPos);
  yPos += 10;
  pdf.text(`Invoice Month: ${invoiceMonth}`, 20, yPos);
  yPos += 10;
  pdf.text(`Invoice For: Services`, 20, yPos);
  yPos += 10;
  pdf.text(`HSN No.: 465757`, 20, yPos);
  yPos += 10;
  pdf.text(`Employee Name: ${invoice.candidate || 'Prathamesh Kadam'}`, 20, yPos);
  yPos += 10;
  pdf.text(`Employee Engagement Code: ENG-0020`, 20, yPos);

  // Right column - Billed To
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(11);
  pdf.text('Billed To :-', 120, yStart);

  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(9);
  yPos = yStart + 12;
  pdf.text(`${invoice.client || 'saurabh'}`, 120, yPos);
  yPos += 20;
  pdf.text('GST No:', 120, yPos);

  // Billing table - exact format from target image
  yPos = 160;
  addBillingTableExactFormat(pdf, invoice, yPos, baseAmount, cgstAmount, sgstAmount, totalAmount);

  // GST Type information
  yPos = 220;
  addGSTTypeSectionExact(pdf, yPos);

  // Net Payable
  yPos += 15;
  addNetPayableSectionExact(pdf, yPos, totalAmount);

  // Payment Information and Authorized Signatory tables
  yPos += 20;
  addPaymentAndSignatureSectionExact(pdf, yPos);

  // Footer
  pdf.setFontSize(9);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(0, 0, 0);
  pdf.text('Thank you for doing business with us.', 105, 280, { align: 'center' });
};

/**
 * Add RedBeryl logo to PDF - matching target image exactly
 */
const addRedBerylLogoExact = (pdf: jsPDF): void => {
  // Logo position - top right corner matching target image
  const logoX = 150;
  const logoY = 25;

  // Draw the circular logo elements exactly as in target image
  pdf.setFillColor(255, 87, 34); // Orange circle
  pdf.circle(logoX, logoY, 3, 'F');

  pdf.setFillColor(33, 150, 243); // Blue circle
  pdf.circle(logoX + 8, logoY, 3, 'F');

  pdf.setFillColor(76, 175, 80); // Green circle below
  pdf.circle(logoX + 4, logoY + 6, 3, 'F');

  // Company name - Red Beryl
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(255, 87, 34); // Orange color
  pdf.text('Red', logoX + 18, logoY + 2);

  pdf.setTextColor(33, 150, 243); // Blue color
  pdf.text('Beryl', logoX + 35, logoY + 2);

  // TECH SOLUTIONS
  pdf.setFontSize(7);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(66, 66, 66);
  pdf.text('TECH SOLUTIONS', logoX + 18, logoY + 8);

  // Tagline
  pdf.setFontSize(6);
  pdf.setFont('helvetica', 'italic');
  pdf.setTextColor(102, 102, 102);
  pdf.text('Integrates Business With Technology', logoX + 18, logoY + 12);

  // Reset color
  pdf.setTextColor(0, 0, 0);
};

/**
 * Add billing table matching target image exactly
 */
const addBillingTableExactFormat = (
  pdf: jsPDF,
  invoice: Invoice,
  yPos: number,
  baseAmount: number,
  cgstAmount: number,
  sgstAmount: number,
  totalAmount: number
): void => {
  const tableX = 20;
  const tableWidth = 170;

  // Main table border
  pdf.setLineWidth(0.5);
  pdf.rect(tableX, yPos, tableWidth, 40);

  // Header row 1 - Main headers with gray background
  pdf.setFillColor(230, 230, 230); // Gray background
  pdf.rect(tableX, yPos, tableWidth, 12, 'F');
  pdf.rect(tableX, yPos, tableWidth, 12); // Border

  // Column separators for header row 1
  pdf.line(tableX + 35, yPos, tableX + 35, yPos + 12); // Employee Name
  pdf.line(tableX + 60, yPos, tableX + 60, yPos + 12); // Joining Date
  pdf.line(tableX + 85, yPos, tableX + 85, yPos + 12); // Rate
  pdf.line(tableX + 110, yPos, tableX + 110, yPos + 12); // Bill Amount
  pdf.line(tableX + 135, yPos, tableX + 135, yPos + 12); // GST

  // Header text row 1
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(8);
  pdf.setTextColor(0, 0, 0);
  pdf.text('Employee', tableX + 5, yPos + 5);
  pdf.text('Name', tableX + 8, yPos + 9);
  pdf.text('Joining', tableX + 37, yPos + 5);
  pdf.text('Date', tableX + 42, yPos + 9);
  pdf.text('Rate', tableX + 70, yPos + 7);
  pdf.text('Bill', tableX + 92, yPos + 5);
  pdf.text('Amount', tableX + 90, yPos + 9);
  pdf.text('GST', tableX + 120, yPos + 7);
  pdf.text('Total Bill', tableX + 140, yPos + 5);
  pdf.text('Amount', tableX + 143, yPos + 9);

  // Header row 2 - GST sub-headers with gray background
  yPos += 12;
  pdf.setFillColor(230, 230, 230); // Gray background
  pdf.rect(tableX + 110, yPos, 60, 10, 'F');
  pdf.rect(tableX + 110, yPos, 60, 10); // Border

  // GST column separators
  pdf.line(tableX + 125, yPos, tableX + 125, yPos + 10);
  pdf.line(tableX + 140, yPos, tableX + 140, yPos + 10);
  pdf.line(tableX + 155, yPos, tableX + 155, yPos + 10);

  pdf.setFontSize(7);
  pdf.text('CGST', tableX + 112, yPos + 4);
  pdf.text('@9%', tableX + 114, yPos + 7);
  pdf.text('SGST', tableX + 127, yPos + 4);
  pdf.text('@9%', tableX + 129, yPos + 7);
  pdf.text('IGST', tableX + 142, yPos + 4);
  pdf.text('@18%', tableX + 141, yPos + 7);

  // Data row
  yPos += 10;
  pdf.setFillColor(255, 255, 255); // White background
  pdf.rect(tableX, yPos, tableWidth, 18, 'F');
  pdf.rect(tableX, yPos, tableWidth, 18); // Border

  // Column separators for data row
  pdf.line(tableX + 35, yPos, tableX + 35, yPos + 18);
  pdf.line(tableX + 60, yPos, tableX + 60, yPos + 18);
  pdf.line(tableX + 85, yPos, tableX + 85, yPos + 18);
  pdf.line(tableX + 110, yPos, tableX + 110, yPos + 18);
  pdf.line(tableX + 125, yPos, tableX + 125, yPos + 18);
  pdf.line(tableX + 140, yPos, tableX + 140, yPos + 18);
  pdf.line(tableX + 155, yPos, tableX + 155, yPos + 18);

  // Data text
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(8);
  pdf.text('Prathamesh', tableX + 2, yPos + 7);
  pdf.text('Kadam', tableX + 5, yPos + 13);
  pdf.text('', tableX + 37, yPos + 10); // Empty joining date
  pdf.text('₹50,000.00', tableX + 62, yPos + 10);
  pdf.text('₹50,000.00', tableX + 87, yPos + 10);
  pdf.text('₹4,500.00', tableX + 112, yPos + 10);
  pdf.text('₹4,500.00', tableX + 127, yPos + 10);
  pdf.text('₹0.00', tableX + 142, yPos + 10); // IGST is 0
  pdf.text('₹59,000.00', tableX + 157, yPos + 10);
};

/**
 * Add GST Type section matching target image exactly
 */
const addGSTTypeSectionExact = (pdf: jsPDF, yPos: number): void => {
  // Blue left border bar
  pdf.setFillColor(33, 150, 243); // Blue color
  pdf.rect(20, yPos, 3, 12, 'F');

  // GST Type text
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(9);
  pdf.setTextColor(0, 0, 0);
  pdf.text('GST Type:', 28, yPos + 8);

  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(76, 175, 80); // Green color
  pdf.text('Intra-State (Maharashtra) - CGST (9%) + SGST (9%) = 18%', 70, yPos + 8);

  // Reset color
  pdf.setTextColor(0, 0, 0);
};

/**
 * Add Net Payable section matching target image exactly
 */
const addNetPayableSectionExact = (pdf: jsPDF, yPos: number, totalAmount: number): void => {
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(11);
  pdf.setTextColor(0, 0, 0);
  pdf.text(`Net Payable: ₹59,000.00 /- (Fifty Nine Thousand Only)`, 20, yPos);
};

/**
 * Add payment information and authorized signatory section matching target image exactly
 */
const addPaymentAndSignatureSectionExact = (pdf: jsPDF, yPos: number): void => {
  const tableX = 20;
  const tableWidth = 170;
  const leftColWidth = 85;
  const rightColWidth = 85;

  // Main table border
  pdf.setLineWidth(0.5);
  pdf.rect(tableX, yPos, tableWidth, 50);

  // Vertical separator
  pdf.line(tableX + leftColWidth, yPos, tableX + leftColWidth, yPos + 50);

  // Left column header
  pdf.setFillColor(240, 240, 240); // Light gray background
  pdf.rect(tableX, yPos, leftColWidth, 12, 'F');
  pdf.rect(tableX, yPos, leftColWidth, 12);

  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(9);
  pdf.setTextColor(0, 0, 0);
  pdf.text('Payment Information', tableX + leftColWidth/2, yPos + 8, { align: 'center' });

  // Right column header
  pdf.setFillColor(240, 240, 240); // Light gray background
  pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12, 'F');
  pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12);
  pdf.text('Authorized Signatory', tableX + leftColWidth + rightColWidth/2, yPos + 8, { align: 'center' });

  // Payment information content
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(8);
  let leftYPos = yPos + 18;

  const paymentInfo = [
    'Bank Name: HDFC Bank',
    'Branch Name: MG Road Branch',
    'Account Name: Acme Corporation Pvt Ltd',
    'Account No: **************',
    'IFSC Code: HDFC0001234',
    'Account Type: Current',
    'GSTN: 29**********2Z5',
    'CIN: U12345KA2020PTC012345',
    'PAN No: **********'
  ];

  paymentInfo.forEach((info, index) => {
    pdf.text(info, tableX + 2, leftYPos + (index * 3.5));
  });

  // Authorized signatory content
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(8);
  pdf.text('For RedBeryl Tech Solutions Pvt. Ltd.', tableX + leftColWidth + 5, yPos + 40);
};



