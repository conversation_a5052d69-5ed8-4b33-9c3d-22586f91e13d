<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Format</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>PDF Format Test</h1>
    
    <div class="info">
        <strong>Test Purpose:</strong> This page tests the updated PDF generation format to match your reference screenshot.
    </div>
    
    <div class="test-container">
        <h2>Generate Test Invoice PDF</h2>
        <p>Click the button below to generate a test PDF with the exact format from your screenshot:</p>
        <button onclick="generateTestPDF()">Generate Test PDF</button>
    </div>

    <script>
        // Mock invoice data for testing
        const testInvoice = {
            id: 1,
            issueDate: '2025-07-21',
            joiningDate: '2025-07-21',
            amount: '₹50,000',
            candidate: 'Prathamesh Kadam',
            client: 'saurabh',
            project: 'Website Development',
            invoiceType: 'credit note',
            hsnCode: '998313'
        };

        // Copy the exact PDF generation functions from pdfUtils.ts (updated to match target image exactly)
        function addRedBerylLogoExact(pdf) {
            const logoX = 150;
            const logoY = 25;

            pdf.setFillColor(255, 87, 34); // Orange circle
            pdf.circle(logoX, logoY, 3, 'F');

            pdf.setFillColor(33, 150, 243); // Blue circle
            pdf.circle(logoX + 8, logoY, 3, 'F');

            pdf.setFillColor(76, 175, 80); // Green circle below
            pdf.circle(logoX + 4, logoY + 6, 3, 'F');

            pdf.setFontSize(14);
            pdf.setFont('helvetica', 'bold');
            pdf.setTextColor(255, 87, 34); // Orange color
            pdf.text('Red', logoX + 18, logoY + 2);

            pdf.setTextColor(33, 150, 243); // Blue color
            pdf.text('Beryl', logoX + 35, logoY + 2);

            pdf.setFontSize(7);
            pdf.setFont('helvetica', 'bold');
            pdf.setTextColor(66, 66, 66);
            pdf.text('TECH SOLUTIONS', logoX + 18, logoY + 8);

            pdf.setFontSize(6);
            pdf.setFont('helvetica', 'italic');
            pdf.setTextColor(102, 102, 102);
            pdf.text('Integrates Business With Technology', logoX + 18, logoY + 12);

            pdf.setTextColor(0, 0, 0);
        }

        function addBillingTableExactFormat(pdf, invoice, yPos, baseAmount, cgstAmount, sgstAmount, totalAmount) {
            const tableX = 20;
            const tableWidth = 170;

            // Main table border
            pdf.setLineWidth(0.5);
            pdf.rect(tableX, yPos, tableWidth, 40);

            // Header row 1 - Main headers with gray background
            pdf.setFillColor(230, 230, 230); // Gray background
            pdf.rect(tableX, yPos, tableWidth, 12, 'F');
            pdf.rect(tableX, yPos, tableWidth, 12); // Border

            // Column separators for header row 1
            pdf.line(tableX + 35, yPos, tableX + 35, yPos + 12); // Employee Name
            pdf.line(tableX + 60, yPos, tableX + 60, yPos + 12); // Joining Date
            pdf.line(tableX + 85, yPos, tableX + 85, yPos + 12); // Rate
            pdf.line(tableX + 110, yPos, tableX + 110, yPos + 12); // Bill Amount
            pdf.line(tableX + 135, yPos, tableX + 135, yPos + 12); // GST

            // Header text row 1
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(8);
            pdf.setTextColor(0, 0, 0);
            pdf.text('Employee', tableX + 5, yPos + 5);
            pdf.text('Name', tableX + 8, yPos + 9);
            pdf.text('Joining', tableX + 37, yPos + 5);
            pdf.text('Date', tableX + 42, yPos + 9);
            pdf.text('Rate', tableX + 70, yPos + 7);
            pdf.text('Bill', tableX + 92, yPos + 5);
            pdf.text('Amount', tableX + 90, yPos + 9);
            pdf.text('GST', tableX + 120, yPos + 7);
            pdf.text('Total Bill', tableX + 140, yPos + 5);
            pdf.text('Amount', tableX + 143, yPos + 9);

            // Header row 2 - GST sub-headers with gray background
            yPos += 12;
            pdf.setFillColor(230, 230, 230); // Gray background
            pdf.rect(tableX + 110, yPos, 60, 10, 'F');
            pdf.rect(tableX + 110, yPos, 60, 10); // Border

            // GST column separators
            pdf.line(tableX + 125, yPos, tableX + 125, yPos + 10);
            pdf.line(tableX + 140, yPos, tableX + 140, yPos + 10);
            pdf.line(tableX + 155, yPos, tableX + 155, yPos + 10);

            pdf.setFontSize(7);
            pdf.text('CGST', tableX + 112, yPos + 4);
            pdf.text('@9%', tableX + 114, yPos + 7);
            pdf.text('SGST', tableX + 127, yPos + 4);
            pdf.text('@9%', tableX + 129, yPos + 7);
            pdf.text('IGST', tableX + 142, yPos + 4);
            pdf.text('@18%', tableX + 141, yPos + 7);

            // Data row
            yPos += 10;
            pdf.setFillColor(255, 255, 255); // White background
            pdf.rect(tableX, yPos, tableWidth, 18, 'F');
            pdf.rect(tableX, yPos, tableWidth, 18); // Border

            // Column separators for data row
            pdf.line(tableX + 35, yPos, tableX + 35, yPos + 18);
            pdf.line(tableX + 60, yPos, tableX + 60, yPos + 18);
            pdf.line(tableX + 85, yPos, tableX + 85, yPos + 18);
            pdf.line(tableX + 110, yPos, tableX + 110, yPos + 18);
            pdf.line(tableX + 125, yPos, tableX + 125, yPos + 18);
            pdf.line(tableX + 140, yPos, tableX + 140, yPos + 18);
            pdf.line(tableX + 155, yPos, tableX + 155, yPos + 18);

            // Data text
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(8);
            pdf.text('Prathamesh', tableX + 2, yPos + 7);
            pdf.text('Kadam', tableX + 5, yPos + 13);
            pdf.text('', tableX + 37, yPos + 10); // Empty joining date
            pdf.text('₹50,000.00', tableX + 62, yPos + 10);
            pdf.text('₹50,000.00', tableX + 87, yPos + 10);
            pdf.text('₹4,500.00', tableX + 112, yPos + 10);
            pdf.text('₹4,500.00', tableX + 127, yPos + 10);
            pdf.text('₹0.00', tableX + 142, yPos + 10); // IGST is 0
            pdf.text('₹59,000.00', tableX + 157, yPos + 10);
        }

        function addGSTTypeSectionExact(pdf, yPos) {
            // Blue left border bar
            pdf.setFillColor(33, 150, 243);
            pdf.rect(20, yPos, 3, 12, 'F');

            // GST Type text
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(9);
            pdf.setTextColor(0, 0, 0);
            pdf.text('GST Type:', 28, yPos + 8);

            pdf.setFont('helvetica', 'normal');
            pdf.setTextColor(76, 175, 80);
            pdf.text('Intra-State (Maharashtra) - CGST (9%) + SGST (9%) = 18%', 70, yPos + 8);

            pdf.setTextColor(0, 0, 0);
        }

        function addNetPayableSectionExact(pdf, yPos, totalAmount) {
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(11);
            pdf.setTextColor(0, 0, 0);
            pdf.text('Net Payable: ₹59,000.00 /- (Fifty Nine Thousand Only)', 20, yPos);
        }

        function addPaymentAndSignatureSectionExact(pdf, yPos) {
            const tableX = 20;
            const tableWidth = 170;
            const leftColWidth = 85;
            const rightColWidth = 85;

            pdf.setLineWidth(0.5);
            pdf.rect(tableX, yPos, tableWidth, 50);

            pdf.line(tableX + leftColWidth, yPos, tableX + leftColWidth, yPos + 50);

            // Headers
            pdf.setFillColor(240, 240, 240);
            pdf.rect(tableX, yPos, leftColWidth, 12, 'F');
            pdf.rect(tableX, yPos, leftColWidth, 12);

            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(9);
            pdf.setTextColor(0, 0, 0);
            pdf.text('Payment Information', tableX + leftColWidth/2, yPos + 8, { align: 'center' });

            pdf.setFillColor(240, 240, 240);
            pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12, 'F');
            pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12);
            pdf.text('Authorized Signatory', tableX + leftColWidth + rightColWidth/2, yPos + 8, { align: 'center' });

            // Payment information content
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(8);
            let leftYPos = yPos + 18;

            const paymentInfo = [
                'Bank Name: HDFC Bank',
                'Branch Name: MG Road Branch',
                'Account Name: Acme Corporation Pvt Ltd',
                'Account No: **************',
                'IFSC Code: HDFC0001234',
                'Account Type: Current',
                'GSTN: 29**********2Z5',
                'CIN: U12345KA2020PTC012345',
                'PAN No: **********'
            ];

            paymentInfo.forEach((info, index) => {
                pdf.text(info, tableX + 2, leftYPos + (index * 3.5));
            });

            // Authorized signatory content
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(8);
            pdf.text('For RedBeryl Tech Solutions Pvt. Ltd.', tableX + leftColWidth + 5, yPos + 40);
        }

        function generateTestPDF() {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF();

            const baseAmount = 50000;
            const cgstAmount = baseAmount * 0.09;
            const sgstAmount = baseAmount * 0.09;
            const totalAmount = baseAmount + cgstAmount + sgstAmount;

            const invoiceDate = '07/21/2025';
            const invoiceNumber = 'RB/25-26/001';
            const invoiceMonth = 'JULY 2025';

            // Add RedBeryl logo - top right corner (exact format)
            addRedBerylLogoExact(pdf);

            // INVOICE header - centered and underlined
            pdf.setFontSize(16);
            pdf.setFont('helvetica', 'bold');
            pdf.text('INVOICE', 105, 50, { align: 'center' });

            // Add underline for INVOICE
            pdf.setLineWidth(1);
            pdf.line(85, 52, 125, 52);

            // Two-column layout for Invoice Details and Billed To
            let yStart = 70;

            // Left column - Invoice Details
            pdf.setFontSize(11);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Invoice Details :-', 20, yStart);

            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);

            let yPos = yStart + 12;
            pdf.text(`Invoice Date: ${invoiceDate}`, 20, yPos);
            yPos += 10;
            pdf.text(`Invoice No.: ${invoiceNumber}`, 20, yPos);
            yPos += 10;
            pdf.text(`Invoice Month: ${invoiceMonth}`, 20, yPos);
            yPos += 10;
            pdf.text(`Invoice For: Services`, 20, yPos);
            yPos += 10;
            pdf.text(`HSN No.: 465757`, 20, yPos);
            yPos += 10;
            pdf.text(`Employee Name: Prathamesh Kadam`, 20, yPos);
            yPos += 10;
            pdf.text(`Employee Engagement Code: ENG-0020`, 20, yPos);

            // Right column - Billed To
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(11);
            pdf.text('Billed To :-', 120, yStart);

            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);
            yPos = yStart + 12;
            pdf.text('saurabh', 120, yPos);
            yPos += 20;
            pdf.text('GST No:', 120, yPos);

            // Billing table - exact format from target image
            yPos = 160;
            addBillingTableExactFormat(pdf, testInvoice, yPos, baseAmount, cgstAmount, sgstAmount, totalAmount);

            // GST Type information
            yPos = 215;
            addGSTTypeSectionExact(pdf, yPos);

            // Net Payable
            yPos += 15;
            addNetPayableSectionExact(pdf, yPos, totalAmount);

            // Payment Information and Authorized Signatory tables
            yPos += 20;
            addPaymentAndSignatureSectionExact(pdf, yPos);

            // Footer
            pdf.setFontSize(9);
            pdf.setFont('helvetica', 'normal');
            pdf.setTextColor(0, 0, 0);
            pdf.text('Thank you for doing business with us.', 105, 280, { align: 'center' });

            pdf.save('test-invoice-exact-target-format.pdf');
        }
    </script>
</body>
</html>
