import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Cloud, CloudUpload, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import oneDriveService, { OneDriveUploadResponse } from '@/services/oneDriveService';
import { generateInvoicePdfBlob } from '@/utils/pdfUtils';
import OneDriveAuthModal from '@/components/OneDriveAuthModal';
import { Invoice } from '@/types/invoice';

interface InvoiceOneDriveButtonProps {
  invoice: Invoice;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  onUploadSuccess?: (response: OneDriveUploadResponse) => void;
  onUploadError?: (error: string) => void;
}

const InvoiceOneDriveButton: React.FC<InvoiceOneDriveButtonProps> = ({
  invoice,
  variant = 'ghost',
  size = 'sm',
  className = '',
  onUploadSuccess,
  onUploadError
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleAuthenticate = () => {
    setShowAuthModal(true);
  };

  const handleAuthSuccess = (accessToken: string) => {
    setIsAuthenticated(true);
    setShowAuthModal(false);
    toast.success('Successfully authenticated with OneDrive!');
    // After authentication, proceed with upload
    performUpload();
  };

  const handleAuthError = (error: string) => {
    setShowAuthModal(false);
    setUploadStatus('error');
    toast.error('Authentication failed', {
      description: error
    });
    if (onUploadError) {
      onUploadError(error);
    }
  };

  const generatePdfContent = async (): Promise<Blob> => {
    console.log('=== PDF GENERATION STARTED ===');
    console.log('Invoice ID:', invoice.id);
    console.log('Invoice Number:', invoice.invoiceNumber);

    // Validate invoice data first
    if (!invoice || !invoice.id) {
      throw new Error('Invalid invoice data: Invoice ID is missing');
    }

    if (!invoice.invoiceNumber) {
      throw new Error('Invalid invoice data: Invoice number is missing');
    }

    // Always try client-side PDF generation first for reliability
    try {
      console.log('Step 1: Generating PDF using client-side method...');
      const clientPdf = await generateInvoicePdfBlob(invoice);

      if (clientPdf && clientPdf.size > 0) {
        console.log(`✓ Client-side PDF generated successfully (${clientPdf.size} bytes)`);
        return clientPdf;
      } else {
        throw new Error('Client-side PDF generation returned empty blob');
      }
    } catch (clientError) {
      console.warn('Client-side PDF generation failed, trying backend...', clientError);

      // Fallback to backend PDF generation
      try {
        console.log('Step 2: Testing backend availability...');
        const testResponse = await fetch('/api/invoice-generation/test', {
          method: 'GET',
          headers: {
            'Accept': 'text/plain',
            'Cache-Control': 'no-cache'
          },
          signal: AbortSignal.timeout(5000)
        });

        if (!testResponse.ok) {
          throw new Error(`Backend test failed: ${testResponse.status}`);
        }

        console.log('✓ Backend is available, trying backend PDF generation...');

        // Determine the correct invoice ID for the backend
        let invoiceId: string | number;

        // Handle different invoice ID formats
        if (typeof invoice.id === 'number') {
          invoiceId = invoice.id;
        } else if (typeof invoice.id === 'string') {
          // For invoice "RB/25-26/001", use ID 22 as confirmed working
          if (invoice.invoiceNumber === 'RB/25-26/001') {
            invoiceId = 22;
            console.log(`Using confirmed mapping: ${invoice.invoiceNumber} -> ID ${invoiceId}`);
          } else {
            // Try to extract numeric ID from string
            const numericMatch = invoice.id.match(/(\d+)/);
            if (numericMatch) {
              invoiceId = parseInt(numericMatch[1], 10);
            } else {
              // If no numeric ID found, use the string as-is
              invoiceId = invoice.id;
            }
          }
        } else {
          invoiceId = invoice.id;
        }

        // Use the correct endpoint format: /api/invoice-generation/public/pdf/{invoiceId}
        const pdfUrl = `/api/invoice-generation/public/pdf/${invoiceId}`;
        console.log(`Fetching PDF from: ${pdfUrl}`);

        const response = await fetch(pdfUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
            'Cache-Control': 'no-cache'
          },
          signal: AbortSignal.timeout(15000) // 15 seconds timeout
        });

        if (response.ok) {
          const blob = await response.blob();
          if (blob.size > 0) {
            console.log(`✓ Successfully fetched PDF from backend (${blob.size} bytes)`);
            return blob;
          } else {
            throw new Error('Backend returned empty PDF');
          }
        } else {
          const errorText = await response.text();
          throw new Error(`Backend error: ${response.status} - ${errorText}`);
        }

      } catch (backendError) {
        console.error('Both client-side and backend PDF generation failed');
        console.error('Client error:', clientError);
        console.error('Backend error:', backendError);

        // If both methods fail, throw a comprehensive error
        throw new Error(`PDF generation failed: Client-side error: ${clientError instanceof Error ? clientError.message : String(clientError)}. Backend error: ${backendError instanceof Error ? backendError.message : String(backendError)}`);
      }
    }
  };

  const performUpload = async () => {
    try {
      setIsUploading(true);
      setUploadStatus('idle');

      console.log('=== ONEDRIVE UPLOAD PROCESS STARTED ===');
      console.log('Invoice ID:', invoice.id);
      console.log('Invoice Number:', invoice.invoiceNumber);

      // Validate invoice data first
      if (!invoice || !invoice.id) {
        throw new Error('Invalid invoice data: Invoice ID is missing');
      }

      if (!invoice.invoiceNumber) {
        throw new Error('Invalid invoice data: Invoice number is missing');
      }

      // Step 1: Generate PDF content
      console.log('Step 1: Generating PDF content...');
      const pdfBlob = await generatePdfContent();

      // Validate PDF content
      if (!pdfBlob || pdfBlob.size === 0) {
        throw new Error('Generated PDF is empty or invalid');
      }

      console.log(`✓ PDF generated successfully: ${pdfBlob.size} bytes, type: ${pdfBlob.type}`);

      // Step 2: Check OneDrive authentication
      console.log('Step 2: Checking OneDrive authentication...');
      const authStatus = await oneDriveService.checkAuthentication();

      if (!authStatus.authenticated) {
        console.log('OneDrive authentication required');
        setIsUploading(false);
        handleAuthenticate();
        return;
      }

      console.log('✓ OneDrive authentication verified');

      // Step 3: Upload to OneDrive
      console.log('Step 3: Uploading PDF to OneDrive...');
      const fileName = `Invoice_${invoice.invoiceNumber.replace(/[\/\\:*?"<>|]/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;

      const response = await oneDriveService.uploadPdf(pdfBlob, fileName);
      console.log('OneDrive upload response:', response);

      if (response && response.success) {
        setUploadStatus('success');
        console.log('✓ OneDrive upload successful!');

        toast.success('Invoice PDF saved to OneDrive!', {
          description: `File: ${response.fileName || fileName}`,
          action: response.webUrl ? {
            label: 'Open in OneDrive',
            onClick: () => window.open(response.webUrl, '_blank')
          } : undefined
        });

        if (onUploadSuccess) {
          onUploadSuccess(response);
        }
      } else {
        throw new Error(response?.error || response?.message || 'OneDrive upload failed - no response received');
      }

    } catch (error) {
      setUploadStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      console.error('✗ OneDrive upload error:', error);

      // Provide user-friendly error messages
      let userFriendlyMessage = 'Failed to save to OneDrive';
      let description = errorMessage;

      if (errorMessage.includes('Invalid invoice data')) {
        description = 'Invoice data is incomplete. Please check the invoice details and try again.';
      } else if (errorMessage.includes('authentication') || errorMessage.includes('token') || errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
        description = 'Authentication required. Please authenticate with OneDrive first.';
        // Trigger authentication modal
        setTimeout(() => handleAuthenticate(), 1000);
      } else if (errorMessage.includes('PDF') || errorMessage.includes('generate')) {
        description = 'Failed to generate PDF. Please try again.';
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('timeout')) {
        description = 'Network error. Please check your connection and try again.';
      } else if (errorMessage.includes('backend') || errorMessage.includes('server')) {
        description = 'Server error. Please try again later.';
      } else if (errorMessage.includes('no response received')) {
        description = 'OneDrive service is not responding. Please try again.';
      }

      toast.error(userFriendlyMessage, {
        description: description,
        action: {
          label: 'Retry',
          onClick: () => performUpload()
        }
      });

      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
      // Reset status after 5 seconds to allow user to see the result
      setTimeout(() => setUploadStatus('idle'), 5000);
    }
  };

  const handleUpload = async () => {
    try {
      console.log('=== ONEDRIVE UPLOAD INITIATED ===');
      console.log('Invoice data:', {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        client: invoice.client?.name || 'Unknown',
        candidate: invoice.candidate?.name || 'Unknown'
      });

      // Pre-flight validation
      if (!invoice || !invoice.id) {
        toast.error('Invalid Invoice', {
          description: 'Invoice ID is missing. Please refresh the page and try again.'
        });
        return;
      }

      if (!invoice.invoiceNumber) {
        toast.error('Invalid Invoice', {
          description: 'Invoice number is missing. Please check the invoice details.'
        });
        return;
      }

      // Check OneDrive authentication status
      console.log('Checking OneDrive authentication...');
      const authStatus = await oneDriveService.checkAuthentication();

      if (!authStatus.authenticated) {
        console.log('User not authenticated, showing authentication modal...');
        toast.info('Authentication Required', {
          description: 'Please authenticate with OneDrive to save the invoice.'
        });
        setShowAuthModal(true);
        return;
      }

      console.log('✓ OneDrive authentication verified');
      setIsAuthenticated(true);

      // Proceed with upload
      await performUpload();

    } catch (error) {
      console.error('Error in upload handler:', error);
      setUploadStatus('error');

      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      let description = 'Please try again.';

      if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        description = 'Network error. Please check your connection and try again.';
      } else if (errorMessage.includes('timeout')) {
        description = 'Request timed out. Please try again.';
      } else if (errorMessage.includes('authentication')) {
        description = 'Authentication failed. Please try authenticating again.';
      }

      toast.error('Upload Error', {
        description: description
      });
    }
  };

  const getButtonIcon = () => {
    if (isUploading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (uploadStatus === 'success') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    
    if (uploadStatus === 'error') {
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    }
    
    return <CloudUpload className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isUploading) {
      return 'Saving to OneDrive...';
    }

    if (uploadStatus === 'success') {
      return 'Saved to OneDrive';
    }

    if (uploadStatus === 'error') {
      return 'Upload Failed - Retry';
    }

    return 'Save to OneDrive';
  };

  const getButtonVariant = () => {
    if (uploadStatus === 'success') {
      return 'default';
    }
    
    if (uploadStatus === 'error') {
      return 'destructive';
    }
    
    return variant;
  };

  const getTooltipText = () => {
    if (!invoice || !invoice.id) {
      return 'Invalid invoice data';
    }
    if (!invoice.invoiceNumber) {
      return 'Invoice number is missing';
    }
    if (isUploading) {
      return 'Uploading to OneDrive...';
    }
    if (uploadStatus === 'success') {
      return 'Successfully saved to OneDrive';
    }
    if (uploadStatus === 'error') {
      return 'Upload failed - Click to retry';
    }
    return `Save Invoice ${invoice.invoiceNumber} to OneDrive`;
  };

  const isButtonDisabled = () => {
    return isUploading || !invoice || !invoice.id || !invoice.invoiceNumber;
  };

  return (
    <>
      <Button
        onClick={handleUpload}
        disabled={isButtonDisabled()}
        variant={getButtonVariant()}
        size={size}
        className={`${className} transition-all duration-200`}
        title={getTooltipText()}
      >
        {getButtonIcon()}
        {size !== 'icon' && <span className="ml-1">{getButtonText()}</span>}
      </Button>

      <OneDriveAuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthSuccess}
        onError={handleAuthError}
      />
    </>
  );
};

export default InvoiceOneDriveButton;























































































