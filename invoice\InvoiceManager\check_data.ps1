# Check available data
$baseUrl = "http://localhost:8091/api"

Write-Host "Checking available data..." -ForegroundColor Green

# Check invoice types
try {
    $invoiceTypes = Invoke-RestMethod -Uri "$baseUrl/invoice-types" -Method GET
    Write-Host "Invoice Types:" -ForegroundColor Cyan
    foreach ($type in $invoiceTypes) {
        Write-Host "  ID: $($type.id), Name: $($type.name)"
    }
} catch {
    Write-Host "Error getting invoice types: $($_.Exception.Message)" -ForegroundColor Red
}

# Check clients
try {
    $clients = Invoke-RestMethod -Uri "$baseUrl/clients" -Method GET
    Write-Host "`nClients:" -ForegroundColor Cyan
    foreach ($client in $clients) {
        Write-Host "  ID: $($client.id), Name: $($client.name)"
    }
} catch {
    Write-Host "Error getting clients: $($_.Exception.Message)" -ForegroundColor Red
}

# Check existing invoices
try {
    $invoices = Invoke-RestMethod -Uri "$baseUrl/invoices" -Method GET
    Write-Host "`nExisting Invoices:" -ForegroundColor Cyan
    foreach ($invoice in $invoices) {
        Write-Host "  ID: $($invoice.id), Number: $($invoice.invoiceNumber), Client: $($invoice.client.name)"
    }
} catch {
    Write-Host "Error getting invoices: $($_.Exception.Message)" -ForegroundColor Red
}
