# PowerShell script to set up test data and create a proper invoice
$baseUrl = "http://localhost:8091/api"

Write-Host "Setting up test data for invoice system..." -ForegroundColor Green

# Function to make API calls with error handling
function Invoke-ApiCall {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [object]$Body = $null
    )
    
    try {
        $headers = @{
            "Content-Type" = "application/json"
        }
        
        if ($Body) {
            $jsonBody = $Body | ConvertTo-Json -Depth 10
            Write-Host "Making $Method request to: $Url" -ForegroundColor Yellow
            Write-Host "Body: $jsonBody" -ForegroundColor Gray
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Body $jsonBody -Headers $headers
        } else {
            Write-Host "Making $Method request to: $Url" -ForegroundColor Yellow
            $response = Invoke-RestMethod -Uri $Url -Method $Method -Headers $headers
        }
        
        return $response
    } catch {
        Write-Host "Error calling $Url : $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 1. Create a client named "<PERSON>urabh"
Write-Host "`n1. Creating client 'Saurabh'..." -ForegroundColor Cyan
$clientData = @{
    name = "Saurabh"
    email = "<EMAIL>"
    phone = "+91-9876543210"
    address = "Mumbai, India"
    contactPerson = "Saurabh"
}

$client = Invoke-ApiCall -Url "$baseUrl/clients" -Method "POST" -Body $clientData
if ($client) {
    Write-Host "✓ Client created with ID: $($client.id)" -ForegroundColor Green
    $clientId = $client.id
} else {
    Write-Host "✗ Failed to create client" -ForegroundColor Red
    exit 1
}

# 2. Create a project for the client
Write-Host "`n2. Creating project..." -ForegroundColor Cyan
$projectData = @{
    name = "Software Development Project"
    clientId = $clientId
    description = "Full stack development project"
    startDate = "2025-01-01"
    endDate = "2025-12-31"
    status = "ACTIVE"
}

$project = Invoke-ApiCall -Url "$baseUrl/projects" -Method "POST" -Body $projectData
if ($project) {
    Write-Host "✓ Project created with ID: $($project.id)" -ForegroundColor Green
    $projectId = $project.id
} else {
    Write-Host "✗ Failed to create project" -ForegroundColor Red
    exit 1
}

# 3. Create a candidate named "Prathamesh Kadam"
Write-Host "`n3. Creating candidate 'Prathamesh Kadam'..." -ForegroundColor Cyan
$candidateData = @{
    name = "Prathamesh Kadam"
    email = "<EMAIL>"
    phone = "+91-9876543211"
    skills = "Java, Spring Boot, React"
    experience = "5 years"
}

$candidate = Invoke-ApiCall -Url "$baseUrl/candidates" -Method "POST" -Body $candidateData
if ($candidate) {
    Write-Host "✓ Candidate created with ID: $($candidate.id)" -ForegroundColor Green
    $candidateId = $candidate.id
} else {
    Write-Host "✗ Failed to create candidate" -ForegroundColor Red
    exit 1
}

# 4. Get or create invoice type
Write-Host "`n4. Getting invoice types..." -ForegroundColor Cyan
$invoiceTypes = Invoke-ApiCall -Url "$baseUrl/invoice-types"
if ($invoiceTypes -and $invoiceTypes.Count -gt 0) {
    $invoiceTypeId = $invoiceTypes[0].id
    Write-Host "✓ Using invoice type ID: $invoiceTypeId" -ForegroundColor Green
} else {
    Write-Host "✗ No invoice types found" -ForegroundColor Red
    exit 1
}

# 5. Create the invoice
Write-Host "`n5. Creating invoice 'RB/25-26/001'..." -ForegroundColor Cyan
$invoiceData = @{
    invoiceNumber = "RB/25-26/001"
    clientId = $clientId
    projectId = $projectId
    candidateId = $candidateId
    invoiceTypeId = $invoiceTypeId
    billingAmount = 50000.00
    taxAmount = 9000.00
    totalAmount = 59000.00
    invoiceDate = "2025-07-21"
    dueDate = "2025-08-20"
    description = "Software development services for July 2025"
    status = "DRAFT"
}

$invoice = Invoke-ApiCall -Url "$baseUrl/invoices" -Method "POST" -Body $invoiceData
if ($invoice) {
    Write-Host "✓ Invoice created successfully!" -ForegroundColor Green
    Write-Host "  Invoice ID: $($invoice.id)" -ForegroundColor White
    Write-Host "  Invoice Number: $($invoice.invoiceNumber)" -ForegroundColor White
    Write-Host "  Client: $($invoice.client.name)" -ForegroundColor White
    Write-Host "  Amount: $($invoice.totalAmount)" -ForegroundColor White
} else {
    Write-Host "✗ Failed to create invoice" -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ Test data setup completed successfully!" -ForegroundColor Green
Write-Host "You can now test the OneDrive upload functionality with invoice: RB/25-26/001" -ForegroundColor Yellow
